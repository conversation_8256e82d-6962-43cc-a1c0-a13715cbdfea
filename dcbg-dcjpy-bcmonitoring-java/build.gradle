plugins {
	id 'java'
	id 'org.springframework.boot' version '3.4.3'
	id 'io.spring.dependency-management' version '1.1.7'
	id 'com.diffplug.spotless' version '7.0.3'
	id 'jacoco'
	id 'groovy'
}

group = 'com.decurret_dcp.dcjpy'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

springBoot {
	mainClass = 'com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication'
}

repositories {
	mavenCentral()
}

dependencies {
	// Spring Boot starter (Web application)
	implementation 'org.springframework.boot:spring-boot-starter'
	// Spring Context
	implementation 'org.springframework:spring-context'

	// Lombok
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'

	// Spring Boot Starter Web
	implementation 'org.springframework.boot:spring-boot-starter-web'

	// AWS SDK for Java
	implementation platform('software.amazon.awssdk:bom:2.31.50')
	implementation 'software.amazon.awssdk:s3'
	implementation 'software.amazon.awssdk:dynamodb'
	implementation 'software.amazon.awssdk:sqs'

	// Spring Retry
	implementation 'org.springframework.retry:spring-retry:2.0.12'
	implementation 'org.springframework:spring-aspects:7.0.0-M5'

	// Web3j (Ethereum client)
	implementation 'org.web3j:core:5.0.0'

	// SLF4J + Logback (Logging)
	implementation 'org.slf4j:slf4j-api:2.1.0-alpha1'
	implementation 'ch.qos.logback:logback-classic:1.5.18'

	// Structured Logging
	implementation 'net.logstash.logback:logstash-logback-encoder:8.1'

	// Jackson (JSON processing)
	implementation 'com.fasterxml.jackson.core:jackson-databind:2.18.1'

	// UUID
	implementation 'com.github.f4b6a3:uuid-creator:6.1.1'

	// Groovy
	testImplementation 'org.apache.groovy:groovy:4.0.27'

	// Spock
	implementation 'org.apache.groovy:groovy-all:4.0.27'
	testImplementation 'org.spockframework:spock-core:2.4-M6-groovy-4.0'
	testImplementation 'org.spockframework:spock-spring:2.4-M6-groovy-4.0'
	testImplementation platform('org.spockframework:spock-bom:2.4-M6-groovy-4.0')

	// TestContainers for managing Docker containers in tests
	testImplementation 'org.testcontainers:testcontainers:1.17.6'
	testImplementation 'org.testcontainers:spock:1.17.6'

	// Spring Boot Test
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher:1.11.3'
	testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.11.3'

	// Byte Buddy Agent for Mockito
	testImplementation 'net.bytebuddy:byte-buddy-agent:1.15.11'
}

spotless {
	java {
		googleJavaFormat('1.23.0')
		importOrder()
		removeUnusedImports()
		formatAnnotations()
		trimTrailingWhitespace()
		endWithNewline()
	}
	groovyGradle {
		greclipse()
	}
	groovy {
		greclipse()
		importOrder()
		removeSemicolons()
		excludeJava()
	}
}

tasks.withType(GroovyCompile) {
	groovyOptions.forkOptions.jvmArgs = [
		'--add-opens',
		'java.base/java.lang=ALL-UNNAMED'
	]
	sourceCompatibility = JavaVersion.VERSION_21
	targetCompatibility = JavaVersion.VERSION_21
}

// Unit tests task - runs tests from the main bcmonitoring package
tasks.register('unitTest', Test) {
	description = 'Runs unit tests from com.decurret_dcp.dcjpy.bcmonitoring package'
	group = 'verification'

	useJUnitPlatform()
	testLogging {
		events "STARTED", "PASSED", "FAILED", "SKIPPED"
		displayGranularity = 4
	}
	jvmArgs "-javaagent:${classpath.find { it.name.contains('byte-buddy-agent') }}"

	// Only include unit tests from the main bcmonitoring package
	include '**/com/decurret_dcp/dcjpy/bcmonitoring/**/*Spec.groovy'
	include '**/com/decurret_dcp/dcjpy/bcmonitoring/**/*Test.java'

	// Exclude adhoc tests
	exclude '**/adhoc/**'
}

// Adhoc tests task - runs tests from the adhoc package
tasks.register('adhocTest', Test) {
	description = 'Runs adhoc integration tests'
	group = 'verification'

	useJUnitPlatform()
	testLogging {
		events "STARTED", "PASSED", "FAILED", "SKIPPED"
		displayGranularity = 4
	}
	jvmArgs "-javaagent:${classpath.find { it.name.contains('byte-buddy-agent') }}"

	// Only include adhoc tests
	include '**/adhoc/**/*Spec.groovy'
	include '**/adhoc/**/*Test.java'

	// This task should only run if unitTest succeeds
	shouldRunAfter unitTest
}

// Main test task - orchestrates the sequential execution
tasks.named('test') {
	description = 'Runs all tests in sequence: unit tests first, then adhoc tests'

	// Make test task depend on both unit and adhoc tests
	dependsOn unitTest, adhocTest

	// Ensure adhoc tests run after unit tests
	adhocTest.mustRunAfter unitTest

	// Don't run any tests directly in the main test task
	// since we're delegating to the specific test tasks
	exclude '**/*'
}

jacoco {
	toolVersion = '0.8.12'
}

jacocoTestReport {
	dependsOn unitTest, adhocTest
	reports {
		xml.required = true
		html.required = true
	}

	// Collect execution data from both test tasks
	executionData unitTest, adhocTest
}

jacocoTestCoverageVerification {
	dependsOn unitTest, adhocTest
	violationRules {
		rule {
			limit {
				minimum = 0.8
			}
		}
	}

	// Collect execution data from both test tasks
	executionData unitTest, adhocTest
}

// Finalize both test tasks with jacoco report
unitTest.finalizedBy jacocoTestReport
adhocTest.finalizedBy jacocoTestReport